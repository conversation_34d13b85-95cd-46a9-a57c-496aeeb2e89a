import React, { useEffect, useState } from 'react';
import { ArrowDown, CheckCircle, Users, Globe, Award, Star, BookOpen, ArrowRight, TrendingUp, MapPin } from 'lucide-react';
import { Link } from 'react-router-dom';
import useHeroStats from '../hooks/useHeroStats';

const Hero = () => {
  const { stats, loading, error } = useHeroStats();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-orange-50 overflow-hidden">
      {/* Enhanced Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full opacity-10 blur-3xl animate-pulse-slow"></div>
        <div className="absolute top-40 right-32 w-56 h-56 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full opacity-10 blur-3xl animate-pulse-slow" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-32 left-1/4 w-80 h-80 bg-gradient-to-r from-green-400 to-green-600 rounded-full opacity-10 blur-3xl animate-pulse-slow" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-20 right-20 w-64 h-64 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full opacity-10 blur-3xl animate-pulse-slow" style={{animationDelay: '3s'}}></div>

        {/* Geometric Shapes */}
        <div className="absolute top-1/4 left-1/3 w-4 h-4 bg-blue-500 rotate-45 opacity-20 animate-bounce" style={{animationDelay: '0.5s'}}></div>
        <div className="absolute top-3/4 right-1/4 w-6 h-6 bg-orange-500 rounded-full opacity-20 animate-bounce" style={{animationDelay: '1.5s'}}></div>
        <div className="absolute top-1/2 left-1/4 w-3 h-3 bg-green-500 opacity-20 animate-bounce" style={{animationDelay: '2.5s'}}></div>
      </div>

      <div className="container mx-auto container-padding relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className={`text-center lg:text-left space-y-8 ${isVisible ? 'animate-slide-in-left' : 'opacity-0'}`}>
            <div className="space-y-6">
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-4">
                <Star className="h-4 w-4 mr-2" />
                #1 Study Abroad Consultancy
              </div>

              <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight">
                Your Gateway to
                <span className="text-gradient block mt-2">
                  Global Education
                </span>
              </h1>

              <p className="text-xl md:text-2xl text-gray-700 max-w-2xl mx-auto lg:mx-0 leading-relaxed font-medium">
                Transform your dreams into reality with expert guidance for studying abroad.
                We help students navigate their journey to world-class universities with personalized support and proven success.
              </p>
            </div>

            {/* Enhanced Features */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 p-3 bg-white/50 rounded-lg hover-scale">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="text-green-600" size={20} />
                </div>
                <span className="font-semibold text-gray-800">100% Visa Success Rate</span>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-white/50 rounded-lg hover-scale">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="text-blue-600" size={20} />
                </div>
                <span className="font-semibold text-gray-800">Expert Counseling</span>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-white/50 rounded-lg hover-scale">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Award className="text-orange-600" size={20} />
                </div>
                <span className="font-semibold text-gray-800">Scholarship Assistance</span>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-white/50 rounded-lg hover-scale">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Globe className="text-purple-600" size={20} />
                </div>
                <span className="font-semibold text-gray-800">End-to-End Support</span>
              </div>
            </div>

            {/* Enhanced CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link to="/contact">
                <button className="btn-primary group">
                  Book Free Consultation
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </button>
              </Link>
              <Link to="/services">
                <button className="btn-outline group">
                  <BookOpen className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                  Explore Services
                </button>
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap items-center justify-center lg:justify-start gap-6 pt-8">
              <div className="text-sm text-gray-700 font-medium">Trusted by students from</div>
              <div className="flex items-center space-x-4">
                <div className="px-3 py-1 bg-gray-200 rounded-full text-xs font-semibold text-gray-800">IIT</div>
                <div className="px-3 py-1 bg-gray-200 rounded-full text-xs font-semibold text-gray-800">NIT</div>
                <div className="px-3 py-1 bg-gray-200 rounded-full text-xs font-semibold text-gray-800">DU</div>
                <div className="px-3 py-1 bg-gray-200 rounded-full text-xs font-semibold text-gray-800">BHU</div>
              </div>
            </div>
          </div>

          {/* Right Content - Enhanced Visual Elements */}
          <div className={`relative ${isVisible ? 'animate-slide-in-right' : 'opacity-0'}`}>
            <div className="relative z-10">
              {/* Main Card with Enhanced Design */}
              <div className="card-elevated p-8 transform rotate-2 hover:rotate-0 transition-all duration-500 hover-lift">
                <div className="aspect-square bg-gradient-to-br from-blue-50 via-white to-orange-50 rounded-2xl flex items-center justify-center relative overflow-hidden">
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-4 left-4 w-8 h-8 bg-blue-500 rounded-full"></div>
                    <div className="absolute top-8 right-8 w-6 h-6 bg-orange-500 rotate-45"></div>
                    <div className="absolute bottom-8 left-8 w-4 h-4 bg-green-500 rounded-full"></div>
                    <div className="absolute bottom-4 right-4 w-10 h-10 bg-purple-500 rounded-full"></div>
                  </div>

                  <div className="text-center space-y-6 relative z-10">
                    <div className="relative">
                      <BookOpen className="h-20 w-20 text-blue-600 mx-auto" />
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                        <Star className="h-3 w-3 text-white" />
                      </div>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">World-Class Education</h3>
                    <p className="text-gray-700 leading-relaxed font-medium">Access to top universities worldwide with comprehensive support</p>

                    {/* Enhanced Stats Display */}
                    {loading && (
                      <div className="flex justify-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      </div>
                    )}

                    {error && (
                      <p className="text-red-500 text-sm">Unable to load stats</p>
                    )}

                    {stats && (
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="text-center p-3 bg-white/70 rounded-lg border border-gray-200">
                          <div className="text-2xl font-bold text-blue-600">{stats.students_placed}+</div>
                          <div className="text-gray-800 font-medium">Students</div>
                        </div>
                        <div className="text-center p-3 bg-white/70 rounded-lg border border-gray-200">
                          <div className="text-2xl font-bold text-orange-600">{stats.countries}+</div>
                          <div className="text-gray-800 font-medium">Countries</div>
                        </div>
                        <div className="text-center p-3 bg-white/70 rounded-lg border border-gray-200">
                          <div className="text-2xl font-bold text-green-600">{stats.success_rate}%</div>
                          <div className="text-gray-800 font-medium">Success</div>
                        </div>
                        <div className="text-center p-3 bg-white/70 rounded-lg border border-gray-200">
                          <div className="text-2xl font-bold text-purple-600">{stats.years_experience}+</div>
                          <div className="text-gray-800 font-medium">Years</div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced Floating Cards */}
              <div className="absolute -top-6 -left-6 glass rounded-2xl p-6 transform -rotate-6 hover:rotate-0 transition-all duration-300 hover-lift">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-green-100 rounded-xl">
                    <Users className="h-8 w-8 text-green-600" />
                  </div>
                  <div>
                    <div className="font-bold text-gray-900 text-lg">Expert Guidance</div>
                    <div className="text-sm text-gray-700 font-medium">Personalized counseling</div>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-6 -right-6 glass rounded-2xl p-6 transform rotate-6 hover:rotate-0 transition-all duration-300 hover-lift">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-orange-100 rounded-xl">
                    <Award className="h-8 w-8 text-orange-500" />
                  </div>
                  <div>
                    <div className="font-bold text-gray-900 text-lg">Proven Success</div>
                    <div className="text-sm text-gray-700 font-medium">High acceptance rates</div>
                  </div>
                </div>
              </div>

              <div className="absolute top-1/2 -right-10 glass rounded-2xl p-6 transform rotate-12 hover:rotate-0 transition-all duration-300 hover-lift">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-purple-100 rounded-xl">
                    <Globe className="h-8 w-8 text-purple-600" />
                  </div>
                  <div>
                    <div className="font-bold text-gray-900 text-lg">Global Network</div>
                    <div className="text-sm text-gray-700 font-medium">Worldwide connections</div>
                  </div>
                </div>
              </div>

              {/* Additional Floating Elements */}
              <div className="absolute top-1/4 -left-4 w-16 h-16 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute bottom-1/4 -right-4 w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full opacity-20 animate-pulse" style={{animationDelay: '1s'}}></div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="flex flex-col items-center space-y-2">
          <div className="text-xs text-gray-700 font-semibold">Scroll to explore</div>
          <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center relative">
            <div className="w-1 h-3 bg-gradient-to-b from-blue-500 to-orange-500 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
